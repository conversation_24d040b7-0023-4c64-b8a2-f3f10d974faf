'use client';

import { useLogout } from '@/features/auth';
import { usePermissions } from '@/hooks/use-permissions';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { Link, usePathname } from '@/i18n/navigation';
import { clearProjectIdFromCookies } from '@/lib/middleware-utils';
import { useProjectContext } from '@/providers/project-context';
import { useQueryClient } from '@tanstack/react-query';
import {
  ArrowLeft,
  BarChart3,
  Calendar,
  CreditCard,
  GalleryVerticalEnd,
  LogOut,
  MessageSquare,
  Settings,
  Shield,
  UserCheck,
  Users as UsersIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { memo, useCallback, useMemo } from 'react';

import { LanguageSwitcher } from '@/components/language-switcher';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarTrigger,
} from '@/components/ui/sidebar';

// Types
interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
  translationKey: string;
}

// Constants
const PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: BarChart3,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/complaints',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
];

const GENERAL_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Projects',
    url: '/projects',
    icon: BarChart3,
    translationKey: 'projects',
  },
  {
    title: 'Profile',
    url: '/profile',
    icon: UsersIcon,
    translationKey: 'profile',
  },
];

const ADMIN_GENERAL_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Projects',
    url: '/projects',
    icon: BarChart3,
    translationKey: 'projects',
  },
  {
    title: 'CP List',
    url: '/cp-list',
    icon: UserCheck,
    translationKey: 'cpList',
  },
  {
    title: 'Profile',
    url: '/profile',
    icon: UsersIcon,
    translationKey: 'profile',
  },
];

const CONTRACTOR_MENU_ITEMS: MenuItem[] = [
  ...GENERAL_MENU_ITEMS,
  {
    title: 'CP List',
    url: '/cp-list',
    icon: UserCheck,
    translationKey: 'cpList',
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
    translationKey: 'settings',
  },
  {
    title: 'Billing',
    url: '/billing',
    icon: CreditCard,
    translationKey: 'billing',
  },
];

const ADMIN_PROJECT_MENU_ITEMS: MenuItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: BarChart3,
    translationKey: 'dashboard',
  },
  {
    title: 'PMAs',
    url: '/pmas',
    icon: Shield,
    translationKey: 'pmas',
  },
  {
    title: 'Maintenance Logs',
    url: '/maintenance-logs',
    icon: Calendar,
    translationKey: 'maintenanceLogs',
  },
  {
    title: 'Complaints',
    url: '/complaints',
    icon: MessageSquare,
    translationKey: 'complaints',
  },
  {
    title: 'Members',
    url: '/members',
    icon: UsersIcon,
    translationKey: 'members',
  },
];

// Utility functions
const getCleanPathname = (pathname: string): string => {
  return pathname.replace(/^\/(en|ms)/, '') || '/';
};

const isRouteActive = (url: string, pathname: string): boolean => {
  const cleanPathname = getCleanPathname(pathname);

  if (url === '/projects') {
    return cleanPathname === '/projects' || cleanPathname === '/';
  }
  if (url === '/dashboard') {
    return cleanPathname === '/dashboard';
  }
  return cleanPathname.startsWith(url);
};

// Memoized components
const SidebarHeaderComponent = memo(
  ({
    isInProjectContext,
    subtitle,
    onBackToProjects,
  }: {
    isInProjectContext: boolean;
    subtitle: string;
    onBackToProjects: () => void;
  }) => (
    <SidebarHeader className="border-b transition-all duration-300 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:py-3 p-4">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-3 group-data-[collapsible=icon]:hidden">
          <div className="bg-primary text-primary-foreground flex size-8 items-center justify-center rounded-lg shadow-sm transition-all duration-300">
            <GalleryVerticalEnd className="size-4 transition-all duration-300" />
          </div>
          <div className="transition-all duration-300">
            <h1 className="text-lg font-semibold">SimPLE</h1>
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          </div>
        </div>

        <div className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:w-full">
          <SidebarTrigger className="h-7 w-7 transition-all duration-300" />
        </div>
      </div>

      {isInProjectContext && (
        <div className="mt-3 pt-3 border-t">
          <button
            onClick={onBackToProjects}
            className="flex items-center gap-2 text-xs text-muted-foreground hover:text-foreground transition-colors w-full"
            aria-label="Back to Projects"
          >
            <ArrowLeft className="h-3 w-3" />
            Back to Projects
          </button>
        </div>
      )}
    </SidebarHeader>
  ),
);

SidebarHeaderComponent.displayName = 'SidebarHeaderComponent';

const NavigationMenu = memo(
  ({
    menuItems,
    isLoading,
    pathname,
    t,
  }: {
    menuItems: MenuItem[];
    isLoading: boolean;
    pathname: string;
    t: (key: string) => string;
  }) => (
    <SidebarGroup>
      <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 mb-1 group-data-[collapsible=icon]:hidden transition-all duration-300">
        {t('navigation')}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        {isLoading ? (
          <div className="px-3 py-2 text-sm text-muted-foreground group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:text-center">
            <div className="group-data-[collapsible=icon]:hidden">
              Loading menu...
            </div>
            <div className="hidden group-data-[collapsible=icon]:block">
              <div className="animate-pulse size-4 bg-muted rounded mx-auto"></div>
            </div>
          </div>
        ) : (
          <SidebarMenu className="space-y-1 group-data-[collapsible=icon]:space-y-2 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center">
            {menuItems.map((item) => {
              const active = isRouteActive(item.url, pathname);
              const IconComponent = item.icon;

              return (
                <SidebarMenuItem
                  key={item.title}
                  className="group-data-[collapsible=icon]:w-7"
                >
                  <Link
                    href={item.url}
                    className={`
                    flex items-center gap-3 w-full px-3 py-2 rounded-md transition-all duration-300 ease-in-out
                    group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:py-0 group-data-[collapsible=icon]:w-7 group-data-[collapsible=icon]:h-7
                    ${
                      active
                        ? 'bg-primary/10 text-primary font-medium group-data-[collapsible=icon]:bg-primary group-data-[collapsible=icon]:text-primary-foreground group-data-[collapsible=icon]:shadow-md border-l-3 border-l-primary group-data-[collapsible=icon]:border-l-0 ml-1 group-data-[collapsible=icon]:ml-0 group-data-[collapsible=icon]:rounded-lg'
                        : 'hover:bg-accent hover:text-accent-foreground group-data-[collapsible=icon]:hover:bg-primary/20 group-data-[collapsible=icon]:hover:scale-105 group-data-[collapsible=icon]:hover:shadow-md group-data-[collapsible=icon]:hover:rounded-lg'
                    }
                  `}
                    aria-label={t(item.translationKey)}
                  >
                    <IconComponent
                      className={`
                      size-4 transition-all duration-300 group-data-[collapsible=icon]:size-4
                      ${
                        active
                          ? 'text-primary group-data-[collapsible=icon]:text-white group-data-[collapsible=icon]:drop-shadow-sm'
                          : 'text-muted-foreground hover:text-accent-foreground group-data-[collapsible=icon]:hover:text-primary'
                      }
                    `}
                    />
                    <span
                      className={`
                      text-sm transition-all duration-300 group-data-[collapsible=icon]:hidden
                      ${active ? 'text-primary font-medium' : 'hover:text-accent-foreground'}
                    `}
                    >
                      {t(item.translationKey)}
                    </span>
                  </Link>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        )}
      </SidebarGroupContent>
    </SidebarGroup>
  ),
);

NavigationMenu.displayName = 'NavigationMenu';

const SidebarFooterComponent = memo(
  ({
    onLogout,
    isLoggingOut,
  }: {
    onLogout: () => void;
    isLoggingOut: boolean;
  }) => (
    <SidebarFooter className="border-t p-2 transition-all duration-300">
      <div className="group-data-[collapsible=icon]:hidden mb-2">
        <LanguageSwitcher />
      </div>

      <SidebarMenu className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center">
        <SidebarMenuItem className="group-data-[collapsible=icon]:w-7">
          <button
            onClick={onLogout}
            disabled={isLoggingOut}
            className="flex items-center gap-3 w-full px-3 py-2 rounded-md transition-all duration-300 hover:bg-destructive/10 hover:text-destructive disabled:opacity-50 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:py-0 group-data-[collapsible=icon]:w-7 group-data-[collapsible=icon]:h-7 group-data-[collapsible=icon]:hover:scale-105 group-data-[collapsible=icon]:hover:shadow-md group-data-[collapsible=icon]:hover:rounded-lg"
            aria-label={isLoggingOut ? 'Signing Out...' : 'Sign Out'}
          >
            <LogOut className="size-4 transition-all duration-300 group-data-[collapsible=icon]:size-4" />
            <span className="text-sm group-data-[collapsible=icon]:hidden transition-all duration-300">
              {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
            </span>
          </button>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
  ),
);

SidebarFooterComponent.displayName = 'SidebarFooterComponent';

// Main component
export const UnifiedSidebar = memo(() => {
  const pathname = usePathname();
  const router = useRouter();
  const logoutMutation = useLogout();
  const { isLoading, userRole } = usePermissions();
  const { selectedProjectId, clearProject, setSelectedProject } =
    useProjectContext();
  const t = useNavigationTranslations();
  const queryClient = useQueryClient();

  // Memoized values
  const isInProjectContext = useMemo(
    () => Boolean(selectedProjectId),
    [selectedProjectId],
  );

  const currentMenuItems = useMemo(() => {
    if (isInProjectContext) {
      // Admin users get special menu items when in project context
      if (userRole === 'admin') {
        return ADMIN_PROJECT_MENU_ITEMS;
      }
      return PROJECT_MENU_ITEMS;
    }

    // Role-based menu items for general context
    switch (userRole) {
      case 'contractor':
        return CONTRACTOR_MENU_ITEMS;
      case 'admin':
        return ADMIN_GENERAL_MENU_ITEMS;
      case 'viewer':
      default:
        return GENERAL_MENU_ITEMS;
    }
  }, [isInProjectContext, userRole]);

  const subtitle = useMemo(() => {
    return isInProjectContext ? 'Project View' : 'Professional Edition';
  }, [isInProjectContext]);

  // Memoized callbacks
  const handleLogout = useCallback(async () => {
    try {
      clearProject();
      setSelectedProject(null);
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [clearProject, setSelectedProject, logoutMutation]);

  const handleBackToProjects = useCallback(() => {
    try {
      // Immediately clear cookies to prevent refetch
      clearProjectIdFromCookies();
      // Set a flag to indicate user intentionally cleared project
      sessionStorage.setItem('user_cleared_project', 'true');
      // Immediately update query data to trigger re-render
      queryClient.setQueryData(['selectedProject'], null);
      // Clear project context
      clearProject();
      setSelectedProject(null);
      // Navigate after clearing context
      router.replace('/projects');
    } catch (error) {
      console.error('Failed to navigate back to projects:', error);
    }
  }, [clearProject, setSelectedProject, router, queryClient]);

  return (
    <Sidebar
      collapsible="icon"
      className="border-r transition-all duration-300"
    >
      <SidebarHeaderComponent
        isInProjectContext={isInProjectContext}
        subtitle={subtitle}
        onBackToProjects={handleBackToProjects}
      />

      <SidebarContent className="p-2 transition-all duration-300">
        <NavigationMenu
          menuItems={currentMenuItems}
          isLoading={isLoading}
          pathname={pathname}
          t={t}
        />
      </SidebarContent>

      <SidebarFooterComponent
        onLogout={handleLogout}
        isLoggingOut={logoutMutation.isPending}
      />
    </Sidebar>
  );
});

UnifiedSidebar.displayName = 'UnifiedSidebar';
